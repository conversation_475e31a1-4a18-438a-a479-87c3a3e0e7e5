'use client';

import { useState } from 'react';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import CreditLoanForm from '@/components/credit-loan/CreditLoanForm';
import CreditLoanSummary from '@/components/credit-loan/CreditLoanSummary';
import CreditLoanTable from '@/components/credit-loan/CreditLoanTable';
import { CreditLoanInput, CreditLoanResult } from '@/lib/types/creditLoan';
import { calculateCreditLoan } from '@/lib/calculations/creditLoan';

export default function CreditLoanPage() {
  const [result, setResult] = useState<CreditLoanResult | null>(null);
  const [loading, setLoading] = useState(false);

  const handleCalculate = async (input: CreditLoanInput) => {
    setLoading(true);
    try {
      // 模擬計算延遲
      await new Promise(resolve => setTimeout(resolve, 500));

      const calculationResult = calculateCreditLoan(input);
      setResult(calculationResult);
    } catch (error) {
      console.error('計算錯誤:', error);
      // 這裡可以添加錯誤處理
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-800 mb-8">信貸計算</h1>

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
            {/* 表單區域 */}
            <div>
              <h2 className="text-xl font-semibold mb-6">貸款資訊</h2>
              <CreditLoanForm onCalculate={handleCalculate} loading={loading} />
            </div>

            {/* 結果區域 */}
            <div>
              <h2 className="text-xl font-semibold mb-6">計算結果</h2>
              <CreditLoanSummary result={result} loading={loading} />
            </div>
          </div>

          {/* 還款計劃表 */}
          <div className="mt-8">
            <h2 className="text-xl font-semibold mb-6">還款計劃表</h2>
            <CreditLoanTable
              payments={result?.monthlyPayments || []}
              loading={loading}
            />
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}