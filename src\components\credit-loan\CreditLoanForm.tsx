'use client';

import { useState, useEffect } from 'react';
import { InputNumber } from 'primereact/inputnumber';
import { Dropdown } from 'primereact/dropdown';
import { But<PERSON> } from 'primereact/button';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { Message } from 'primereact/message';
import { CreditLoanInput } from '@/lib/types/creditLoan';
import { validateCreditLoanInput } from '@/lib/calculations/creditLoan';

interface CreditLoanFormProps {
  onCalculate: (input: CreditLoanInput) => void;
  loading?: boolean;
}

export default function CreditLoanForm({ onCalculate, loading = false }: CreditLoanFormProps) {
  const [formData, setFormData] = useState<CreditLoanInput>({
    loanAmount: 1000000,
    termMonths: 60,
    rateType: 'single',
    singleRate: 3.5,
    fees: {
      setupFee: 9000,
      monthlyFee: 300,
      otherFees: 0
    }
  });

  const [errors, setErrors] = useState<string[]>([]);

  const rateTypeOptions = [
    { label: '單一利率', value: 'single' },
    { label: '2段式利率', value: 'two-stage' },
    { label: '3段式利率', value: 'three-stage' }
  ];

  const termOptions = [
    { label: '12個月', value: 12 },
    { label: '24個月', value: 24 },
    { label: '36個月', value: 36 },
    { label: '48個月', value: 48 },
    { label: '60個月', value: 60 },
    { label: '72個月', value: 72 },
    { label: '84個月', value: 84 }
  ];

  const handleSubmit = () => {
    const validationErrors = validateCreditLoanInput(formData);
    setErrors(validationErrors);

    if (validationErrors.length === 0) {
      onCalculate(formData);
    }
  };

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateNestedFormData = (parentField: string, childField: string, value: any) => {
    setFormData(prev => {
      const currentParentValue = prev[parentField as keyof CreditLoanInput];
      return {
        ...prev,
        [parentField]: {
          ...(currentParentValue && typeof currentParentValue === 'object' ? currentParentValue : {}),
          [childField]: value
        }
      };
    });
  };

  const updateTwoStageRates = (stage: 'firstStage' | 'secondStage', field: string, value: number | null | undefined) => {
    setFormData(prev => {
      // 確保 twoStageRates 存在且有正確的結構
      const currentTwoStageRates = prev.twoStageRates || {
        firstStage: { months: 12, rate: 2.5 },
        secondStage: { rate: 4.5 }
      };

      // 確保當前階段存在
      const currentStage = currentTwoStageRates[stage] ||
        (stage === 'firstStage' ? { months: 12, rate: 2.5 } : { rate: 4.5 });

      return {
        ...prev,
        twoStageRates: {
          ...currentTwoStageRates,
          [stage]: {
            ...currentStage,
            [field]: value || 0
          }
        }
      };
    });
  };

  const updateThreeStageRates = (stage: 'firstStage' | 'secondStage' | 'thirdStage', field: string, value: number | null | undefined) => {
    setFormData(prev => {
      // 確保 threeStageRates 存在且有正確的結構
      const currentThreeStageRates = prev.threeStageRates || {
        firstStage: { months: 6, rate: 2.0 },
        secondStage: { months: 12, rate: 3.5 },
        thirdStage: { rate: 5.0 }
      };

      // 確保當前階段存在
      let currentStage;
      switch (stage) {
        case 'firstStage':
          currentStage = currentThreeStageRates.firstStage || { months: 6, rate: 2.0 };
          break;
        case 'secondStage':
          currentStage = currentThreeStageRates.secondStage || { months: 12, rate: 3.5 };
          break;
        case 'thirdStage':
          currentStage = currentThreeStageRates.thirdStage || { rate: 5.0 };
          break;
      }

      return {
        ...prev,
        threeStageRates: {
          ...currentThreeStageRates,
          [stage]: {
            ...currentStage,
            [field]: value || 0
          }
        }
      };
    });
  };

  const renderRateInputs = () => {
    switch (formData.rateType) {
      case 'single':
        return (
          <div className="field">
            <label htmlFor="singleRate" className="block text-sm font-medium mb-2">
              年利率 (%)
            </label>
            <InputNumber
              id="singleRate"
              value={formData.singleRate}
              onValueChange={(e) => updateFormData('singleRate', e.value)}
              mode="decimal"
              minFractionDigits={2}
              maxFractionDigits={4}
              min={0}
              max={30}
              suffix="%"
              className="w-full"
            />
          </div>
        );

      case 'two-stage':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="field">
                <label className="block text-sm font-medium mb-2">
                  第1段期數 (月)
                </label>
                <InputNumber
                  value={formData.twoStageRates?.firstStage.months}
                  onValueChange={(e) => updateTwoStageRates('firstStage', 'months', e.value)}
                  min={1}
                  max={formData.termMonths - 1}
                  className="w-full"
                />
              </div>
              <div className="field">
                <label className="block text-sm font-medium mb-2">
                  第1段利率 (%)
                </label>
                <InputNumber
                  value={formData.twoStageRates?.firstStage.rate}
                  onValueChange={(e) => updateTwoStageRates('firstStage', 'rate', e.value)}
                  mode="decimal"
                  minFractionDigits={2}
                  maxFractionDigits={4}
                  min={0}
                  max={30}
                  suffix="%"
                  className="w-full"
                />
              </div>
            </div>
            <div className="field">
              <label className="block text-sm font-medium mb-2">
                第2段利率 (%)
              </label>
              <InputNumber
                value={formData.twoStageRates?.secondStage.rate}
                onValueChange={(e) => updateTwoStageRates('secondStage', 'rate', e.value)}
                mode="decimal"
                minFractionDigits={2}
                maxFractionDigits={4}
                min={0}
                max={30}
                suffix="%"
                className="w-full"
              />
            </div>
          </div>
        );

      case 'three-stage':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="field">
                <label className="block text-sm font-medium mb-2">
                  第1段期數 (月)
                </label>
                <InputNumber
                  value={formData.threeStageRates?.firstStage.months}
                  onValueChange={(e) => updateThreeStageRates('firstStage', 'months', e.value)}
                  min={1}
                  className="w-full"
                />
              </div>
              <div className="field">
                <label className="block text-sm font-medium mb-2">
                  第1段利率 (%)
                </label>
                <InputNumber
                  value={formData.threeStageRates?.firstStage.rate}
                  onValueChange={(e) => updateThreeStageRates('firstStage', 'rate', e.value)}
                  mode="decimal"
                  minFractionDigits={2}
                  maxFractionDigits={4}
                  min={0}
                  max={30}
                  suffix="%"
                  className="w-full"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="field">
                <label className="block text-sm font-medium mb-2">
                  第2段期數 (月)
                </label>
                <InputNumber
                  value={formData.threeStageRates?.secondStage.months}
                  onValueChange={(e) => updateThreeStageRates('secondStage', 'months', e.value)}
                  min={1}
                  className="w-full"
                />
              </div>
              <div className="field">
                <label className="block text-sm font-medium mb-2">
                  第2段利率 (%)
                </label>
                <InputNumber
                  value={formData.threeStageRates?.secondStage.rate}
                  onValueChange={(e) => updateThreeStageRates('secondStage', 'rate', e.value)}
                  mode="decimal"
                  minFractionDigits={2}
                  maxFractionDigits={4}
                  min={0}
                  max={30}
                  suffix="%"
                  className="w-full"
                />
              </div>
            </div>
            <div className="field">
              <label className="block text-sm font-medium mb-2">
                第3段利率 (%)
              </label>
              <InputNumber
                value={formData.threeStageRates?.thirdStage.rate}
                onValueChange={(e) => updateThreeStageRates('thirdStage', 'rate', e.value)}
                mode="decimal"
                minFractionDigits={2}
                maxFractionDigits={4}
                min={0}
                max={30}
                suffix="%"
                className="w-full"
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // 初始化分段利率數據
  useEffect(() => {
    if (formData.rateType === 'two-stage' && !formData.twoStageRates) {
      setFormData(prev => ({
        ...prev,
        twoStageRates: {
          firstStage: { months: 12, rate: 2.5 },
          secondStage: { rate: 4.5 }
        }
      }));
    } else if (formData.rateType === 'three-stage' && !formData.threeStageRates) {
      setFormData(prev => ({
        ...prev,
        threeStageRates: {
          firstStage: { months: 6, rate: 2.0 },
          secondStage: { months: 12, rate: 3.5 },
          thirdStage: { rate: 5.0 }
        }
      }));
    }
  }, [formData.rateType, formData.twoStageRates, formData.threeStageRates]);

  return (
    <Card className="w-full">
      <div className="space-y-6">
        {/* 錯誤訊息 */}
        {errors.length > 0 && (
          <div className="space-y-2">
            {errors.map((error, index) => (
              <Message key={index} severity="error" text={error} className="w-full" />
            ))}
          </div>
        )}

        {/* 基本資訊 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="field">
            <label htmlFor="loanAmount" className="block text-sm font-medium mb-2">
              貸款金額 (元)
            </label>
            <InputNumber
              id="loanAmount"
              value={formData.loanAmount}
              onValueChange={(e) => updateFormData('loanAmount', e.value)}
              mode="currency"
              currency="TWD"
              locale="zh-TW"
              min={10000}
              max={50000000}
              minFractionDigits={0}
              maxFractionDigits={0}
              useGrouping={true}
              className="w-full"
            />
          </div>

          <div className="field">
            <label htmlFor="termMonths" className="block text-sm font-medium mb-2">
              期數 (月)
            </label>
            <Dropdown
              id="termMonths"
              value={formData.termMonths}
              options={termOptions}
              onChange={(e) => updateFormData('termMonths', e.value)}
              className="w-full"
            />
          </div>
        </div>

        <Divider />

        {/* 利率設定 */}
        <div className="space-y-4">
          <div className="field">
            <label htmlFor="rateType" className="block text-sm font-medium mb-2">
              利率計算方式
            </label>
            <Dropdown
              id="rateType"
              value={formData.rateType}
              options={rateTypeOptions}
              onChange={(e) => updateFormData('rateType', e.value)}
              className="w-full"
            />
          </div>

          {renderRateInputs()}
        </div>

        <Divider />

        {/* 相關費用 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">相關費用</h3>

          <div className="flex-items gap-6">
            <div className="field">
              <label className="block text-sm font-medium mb-2">
                開辦費 (元)
              </label>
              <InputNumber
                value={formData.fees.setupFee}
                onValueChange={(e) => updateNestedFormData('fees', 'setupFee', e.value)}
                mode="currency"
                currency="TWD"
                locale="zh-TW"
                min={0}
                minFractionDigits={0}
                maxFractionDigits={0}
                useGrouping={true}
                className="w-full"
              />
            </div>

            <div className="field">
              <label className="block text-sm font-medium mb-2">
                帳管費 (每月)
              </label>
              <InputNumber
                value={formData.fees.monthlyFee}
                onValueChange={(e) => updateNestedFormData('fees', 'monthlyFee', e.value)}
                mode="currency"
                currency="TWD"
                locale="zh-TW"
                min={0}
                minFractionDigits={0}
                maxFractionDigits={0}
                useGrouping={true}
                className="w-full"
              />
            </div>

            <div className="field">
              <label className="block text-sm font-medium mb-2">
                其他費用 (元)
              </label>
              <InputNumber
                value={formData.fees.otherFees}
                onValueChange={(e) => updateNestedFormData('fees', 'otherFees', e.value)}
                mode="currency"
                currency="TWD"
                locale="zh-TW"
                min={0}
                minFractionDigits={0}
                maxFractionDigits={0}
                useGrouping={true}
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* 計算按鈕 */}
        <div className="flex justify-center pt-4">
          <Button
            label="開始計算"
            icon="pi pi-calculator"
            onClick={handleSubmit}
            loading={loading}
            size="large"
            className="px-8"
          />
        </div>
      </div>
    </Card>
  );
}
