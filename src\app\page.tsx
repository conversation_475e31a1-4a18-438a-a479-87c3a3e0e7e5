import { Card } from 'primereact/card';
import { Button } from 'primereact/button';
import Link from 'next/link';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto px-1 py-4">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            專業貸款計算機
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            輕鬆計算信貸和房貸，制定最適合的還款計劃
          </p>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <Card className="shadow-lg hover:shadow-xl transition-shadow">
            <div className="text-center p-6">
              <i className="pi pi-calculator text-6xl text-blue-600 mb-4"></i>
              <h2 className="text-2xl font-bold mb-4">信貸計算</h2>
              <p className="text-gray-600 mb-6">
                支援單一利率、2段式、3段式利率計算，提供詳細還款計劃表
              </p>
              <Link href="/credit-loan">
                <Button
                  label="開始計算"
                  icon="pi pi-arrow-right"
                  className="w-full"
                  size="large"
                />
              </Link>
            </div>
          </Card>

          <Card className="shadow-lg hover:shadow-xl transition-shadow">
            <div className="text-center p-6">
              <i className="pi pi-building text-6xl text-green-600 mb-4"></i>
              <p className="text-gray-600 mb-6">
                計算房屋貸款還款金額，包含寬限期、各項費用，並提供圖表分析
              </p>
              <Link href="/mortgage">
                <Button
                  label="開始計算"
                  icon="pi pi-arrow-right"
                  className="w-full"
                  severity="success"
                  size="large"
                />
              </Link>
            </div>
          </Card>
        </div>

        {/* Features List */}
        <div className="mt-16 max-w-3xl mx-auto">
          <h3 className="text-2xl font-bold text-center mb-8">主要功能</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-center gap-3">
              <i className="pi pi-check-circle text-green-500"></i>
              <span>多種利率計算方式</span>
            </div>
            <div className="flex items-center gap-3">
              <i className="pi pi-check-circle text-green-500"></i>
              <span>詳細還款計劃表</span>
            </div>
            <div className="flex items-center gap-3">
              <i className="pi pi-check-circle text-green-500"></i>
              <span>費用計算與統計</span>
            </div>
            <div className="flex items-center gap-3">
              <i className="pi pi-check-circle text-green-500"></i>
              <span>圖表視覺化分析</span>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}

